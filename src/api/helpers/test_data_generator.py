import faker


class DataGenerator:
    def __init__(self):
        self._faker = faker.Faker()
        self.name = self._faker.name()
        self.campaign_name = self._faker.sentence()
        self.email = self._faker.email()
        self.company = self._faker.company()
        self.email = self._faker.email()
        self.description = self._faker.sentence()
        self.future_date = self._faker.date_between(
            start_date="today", end_date="+30d"
        ).strftime("%Y-%m-%d")
        self.past_date = self._faker.date_between(
            start_date="-30d", end_date="today"
        ).strftime("%Y-%m-%d")
        self.coc = self._faker.random_number(digits=8)
