import json

from playwright.sync_api import APIRequestContext, APIResponse, Request


class LoggedAPIRequestContext:
    def __init__(self, context: APIRequestContext, log_api: bool = True):
        self._context = context
        self._log_api = log_api

    def delete(self, url: str, **kwargs) -> APIResponse:
        response = self._context.delete(url, **kwargs)
        self._log("DELETE", response, kwargs)
        return response

    def get(self, url: str, **kwargs) -> APIResponse:
        response = self._context.get(url, **kwargs)
        self._log("GET", response, kwargs)
        return response

    def post(self, url: str, **kwargs) -> APIResponse:
        response = self._context.post(url, **kwargs)
        self._log("POST", response, kwargs)
        return response

    def put(self, url: str, **kwargs) -> APIResponse:
        response = self._context.put(url, **kwargs)
        self._log("PUT", response, kwargs)
        return response

    def patch(self, url: str, **kwargs) -> APIResponse:
        response = self._context.patch(url, **kwargs)
        self._log("PATCH", response, kwargs)
        return response

    def fetch(self, url_or_request: str | Request, **kwargs) -> APIResponse:
        response = self._context.fetch(url_or_request, **kwargs)
        self._log(f"FETCH-{kwargs.get('method', '')}", response, kwargs)
        return response

    def dispose(self, **kwargs):
        return self._context.dispose(**kwargs)

    def _log(self, method: str, response: APIResponse, options: dict | None = None):
        if not self._log_api:
            return

        print("\nApi Request")
        print(f"method: {method}")
        print(f"url: {response.url}")
        print(f"status: {response.status}")

        if options:
            if "headers" in options:
                print("headers:", options["headers"])
            if "params" in options:
                print("params:", options["params"])
            if "data" in options:
                print("REQUEST:")
                print(json.dumps(options["data"], indent=2))

        body_text = response.text().strip()
        if body_text:
            print("RESPONSE:")
            if self._has_json_response(response):
                print(json.dumps(response.json(), indent=2))
            else:
                print(body_text)

    @staticmethod
    def _has_json_response(response: APIResponse) -> bool:
        content_type = response.headers.get("content-type", "")
        return "application/json" in content_type.lower()
