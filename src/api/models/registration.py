from pydantic import BaseModel, EmailStr


class CampaignRegistrationRequest(BaseModel):
    """Request model for campaign registration"""

    name_campaign: str
    date_campaign: str
    organisation_name: str
    description_campaign: str
    contact_name: str
    contact_email: str
    accept_contact: bool
    accept_price: bool


class CampaignRegistrationResponse(BaseModel):
    """Response model for campaign registration"""

    name_campaign: str
    description_campaign: str
    date_campaign: str
    organisation_name: str
    contact_name: str
    contact_email: EmailStr


class AiRegistrationRequest(BaseModel):
    """Request model for AI registration"""

    accept_conditions: bool
    accept_price: bool
    accept_privacy_statement: bool
    contact_email: EmailStr
    contact_name: str
    flow: str
    organisation_coc: int
    organisation_name: str


class AiRegistrationResponse(BaseModel):
    """Response model for AI registration"""

    flow: str
    organisation_name: str
    contact_name: str
    contact_email: EmailStr
