from playwright.sync_api import APIResponse, expect
from http import HTT<PERSON>tatus
from pydantic import BaseModel, ValidationError, parse_obj_as


class ApiResponseHandler:
    @staticmethod
    def handle_success(response: APIResponse, schema: type[BaseModel]):
        return ApiResponseHandler._handle_internal(response, None, schema)

    @staticmethod
    def handle(response: APIResponse, expected_status: HTTPStatus, schema: type[BaseModel]):
        return ApiResponseHandler._handle_internal(response, expected_status, schema)

    @staticmethod
    def handle_array_success(response: APIResponse, schema: type[BaseModel]):
        return ApiResponseHandler._handle_array_internal(response, None, schema)

    @staticmethod
    def handle_array(response: APIResponse, expected_status: HTTPStatus, schema: type[BaseModel]):
        return ApiResponseHandler._handle_array_internal(response, expected_status, schema)

    @staticmethod
    def _handle_internal(response: APIResponse, expected_status: HTTPStatus | None, schema: type[BaseModel]):
        json_body = ApiResponseHandler._validate_and_parse_response(response, expected_status)
        return ApiResponseHandler._map_json(json_body, schema)

    @staticmethod
    def _handle_array_internal(response: APIResponse, expected_status: HTTPStatus | None, schema: type[BaseModel]):
        json_body = ApiResponseHandler._validate_and_parse_response(response, expected_status)
        return ApiResponseHandler._map_json_array(json_body, schema)

    @staticmethod
    def _validate_and_parse_response(response: APIResponse, expected_status: HTTPStatus | None = None):
        if expected_status:
            assert response.status == expected_status, f"Expected {expected_status}, got {response.status}.\nBody: {response.text()}"
        else:
            expect(response).to_be_ok()
        return response.json()

    @staticmethod
    def _map_json(json_body: dict, schema: type[BaseModel]):
        try:
            return schema.model_validate(json_body)
        except ValidationError as e:
            raise AssertionError(f"Response validation failed:\n{e}")

    @staticmethod
    def _map_json_array(json_body: list, schema: type[BaseModel]):
        try:
            return parse_obj_as(list[schema], json_body)
        except ValidationError as e:
            raise AssertionError(f"Response validation failed:\n{e}")