import requests
import os

from auth.test_user_provider import <PERSON><PERSON><PERSON><PERSON><PERSON>, Role



def _generate_auth_token(provider: UserProvider, role: Role) -> str:
    user = provider.get_test_user(role)

    base_url = "https://test.stramigo.com"
    auth_endpoint = f"{base_url}/api/auth/token/"

    payload = {
        "username": user.email,
        "password": user.password,
    }

    response = requests.post(auth_endpoint, json=payload)
    if response.status_code != 200:
        raise ValueError(f"Failed to generate auth token with status code {response.status_code} and message: {response.content}")

    return response.json()["token"]


def _fetch_mails() -> dict:

    auth_token = _generate_auth_token(user_provider, Role.ADMIN)
    base_url = "https://test.stramigo.com"
    mails_endpoint = f"{base_url}/api/mails/"

    headers = {
        "Authorization": f"token {auth_token}"
    }

    response = requests.get(mails_endpoint, headers=headers)
    if response.status_code != 200:
        raise ValueError("Failed to get emails.")

    return response.json()




if __name__ == "__main__":
    user_provider = UserProvider(
        admin_email="<EMAIL>",
        admin_password="N1M686hIcHLiIY1w9hP5ynd07xWskLcz",
        user_email="<EMAIL>",
        user_password="test",
    )
    print(_generate_auth_token(user_provider, Role.ADMIN))

    print(_fetch_mails())
