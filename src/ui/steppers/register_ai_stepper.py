from src.ui.domhandlers.register_ai_page import RegisterAiPage
from playwright.sync_api import Page

class RegisterAiStepper(RegisterAiPage):
    def __init__(self, page: Page):
        super().__init__(page)

    def fill_organisation_name(self, organisation_name: str):
        self.register_organisation_name.fill(organisation_name)

    def fill_organisation_coc(self, organisation_coc: str):
        self.register_organisation_coc.fill(organisation_coc)

    def fill_contact_name(self, contact_name: str):
        self.register_contact_name.fill(contact_name)

    def fill_contact_email(self, contact_email: str):
        self.register_contact_email.fill(contact_email)

    def accept_terms(self):
        self.register_accept_terms.check()

    def accept_privacy(self):
        self.register_accept_privacy.check()

    def accept_price(self):
        self.register_accept_price.check()

    def click_submit(self):
        self.register_submit_button.click()

    def register_ai_demo(self, organisation_name: str, organisation_coc: str, contact_name: str, contact_email: str):
        self.fill_organisation_name(organisation_name)
        self.fill_organisation_coc(organisation_coc)
        self.fill_contact_name(contact_name)
        self.fill_contact_email(contact_email)
        self.accept_terms()
        self.accept_privacy()
        self.click_submit()

    def register_ai_complete(self, organisation_name: str, organisation_coc: str, contact_name: str, contact_email: str):
        self.fill_organisation_name(organisation_name)
        self.fill_organisation_coc(organisation_coc)
        self.fill_contact_name(contact_name)
        self.fill_contact_email(contact_email)
        self.accept_terms()
        self.accept_privacy()
        self.accept_price()
        self.click_submit()