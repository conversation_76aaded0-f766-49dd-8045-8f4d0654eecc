from playwright.sync_api import Page

from auth.test_user_provider import Role, UserProvider
from src.ui.domhandlers.login_page import LoginPage


class LoginStepper(LoginPage):
    def __init__(self, page: Page):
        super().__init__(page)

    def fill_email(self, email: str):
        self.email_input.fill(email)

    def fill_password(self, password: str):
        self.password_input.fill(password)

    def click_login(self):
        self.login_button.click()

    def click_forgot_password(self):
        self.forgot_password_button.click()

    def click_forgot_password_submit(self):
        self.forgot_password_submit_button.click()

    def login_with_role(self, role: Role, provider: UserProvider):
        user = provider.get_test_user(role)
        self.fill_email(user.email)
        self.fill_password(user.password)
        self.click_login()

    def request_password_reset(self, role: Role, provider: UserProvider):
        user = provider.get_test_user(role)
        self.click_forgot_password()
        self.fill_email(user.email)
        self.click_forgot_password_submit()

