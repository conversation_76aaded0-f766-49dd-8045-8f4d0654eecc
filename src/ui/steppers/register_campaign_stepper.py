from playwright.sync_api import Page

from src.ui.domhandlers.register_campaign_page import RegisterCampaignPage


class RegisterCampaignStepper(RegisterCampaignPage):
    def __init__(self, page: Page):
        super().__init__(page)

    def fill_campaign_name(self, campaign_name: str):
        self.register_campaign_name.fill(campaign_name)

    def fill_campaign_description(self, campaign_description: str):
        self.register_campaign_description.fill(campaign_description)

    def fill_campaign_start_date(self, campaign_start_date: str):
        self.register_campaign_start_date.fill(campaign_start_date)

    def click_next(self):
        self.register_next.click()

    def fill_organisation_name(self, organisation_name: str):
        self.register_organisation_name.fill(organisation_name)

    def fill_contact_name(self, contact_name: str):
        self.register_contact_name.fill(contact_name)

    def fill_contact_email(self, contact_email: str):
        self.register_contact_email.fill(contact_email)

    def accept_contact(self):
        self.register_accept_contact.check()

    def accept_price(self):
        self.register_accept_price.check()

    def click_submit(self):
        self.register_submit_button.click()

    def register_campaign(
        self,
        campaign_name: str,
        campaign_description: str,
        campaign_start_date: str,
        organisation_name: str,
        contact_name: str,
        contact_email: str,
    ):
        self.fill_campaign_name(campaign_name)
        self.fill_campaign_description(campaign_description)
        self.fill_campaign_start_date(campaign_start_date)
        self.click_next()
        self.fill_organisation_name(organisation_name)
        self.fill_contact_name(contact_name)
        self.fill_contact_email(contact_email)
        self.accept_contact()
        self.accept_price()
        self.click_submit()
