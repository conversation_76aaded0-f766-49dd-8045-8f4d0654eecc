from playwright.sync_api import Locator, Page


class RegisterCampaignPage:
    def __init__(self, page: Page):
        self.page = page
        self.register_campaign_name: Locator = page.get_by_test_id(
            "register-campaign-name"
        )
        self.register_campaign_description: Locator = page.get_by_test_id(
            "register-campaign-description"
        )
        self.register_campaign_start_date = page.locator("input[type=date]")

        self.register_next: Locator = page.get_by_test_id("register-next")
        self.register_organisation_name: Locator = page.get_by_test_id(
            "register-organisation-name"
        )
        self.register_contact_name: Locator = page.get_by_test_id(
            "register-contact-name"
        )
        self.register_contact_email: Locator = page.get_by_test_id(
            "register-contact-email"
        )
        self.register_accept_contact: Locator = page.locator(
            "input.form-check-input"
        ).nth(0)
        self.register_accept_price: Locator = page.locator(
            "input.form-check-input"
        ).nth(1)

        self.register_submit_button: Locator = page.get_by_test_id("register-submit")
