from playwright.sync_api import Locator, Page


class LoginPage:
    def __init__(self, page: Page):
        self.page = page
        self.email_input: Locator = page.get_by_test_id("login-email")
        self.password_input: Locator = page.get_by_test_id("login-password")
        self.login_button: Locator = page.get_by_test_id("button-submit-login")
        self.forgot_password_button: Locator = page.get_by_test_id("button-forgot-password")
        self.forgot_password_submit_button: Locator = page.get_by_text("Verzenden")
        self.success_message_reset_password: Locator = page.locator("div.alert.alert-info")
