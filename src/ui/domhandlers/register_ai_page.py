from playwright.sync_api import Page, Locator

class RegisterAiPage:
    def __init__(self, page: Page):
        self.page = page
        self.register_organisation_name: Locator = page.get_by_test_id("register-organisation-name")
        self.register_organisation_coc: Locator = page.get_by_test_id("register-organisation-coc")
        self.register_contact_name: Locator = page.get_by_test_id("register-contact-name")
        self.register_contact_email: Locator = page.get_by_test_id("register-contact-email")
        self.register_submit_button: Locator = page.get_by_test_id("register-submit")
        self.register_accept_terms: Locator = page.locator("input.form-check-input").nth(0)
        self.register_accept_privacy: Locator = page.locator("input.form-check-input").nth(1)
        self.register_accept_price: Locator = page.locator("input.form-check-input").nth(2)