from playwright.sync_api import Page, Locator

class RegisterCampaingPage:
    def __init__(self, page: Page):
        self.page = page
        self.register_campaing_name: Locator = page.get_by_test_id("register-campaing-name")
        self.register_campaing_description: Locator = page.get_by_test_id("register-campaing-description")
        self.register_campaing_start_date: Locator = page.get_by_role("input", name="date")
        self.register_next: Locator = page.get_by_test_id("register-next")
        self.register_organisation_name: Locator = page.get_by_test_id("register-organisation-name")
        self.register_contact_name: Locator = page.get_by_test_id("register-contact-name")
        self.register_contact_email: Locator = page.get_by_test_id("register-contact-email")