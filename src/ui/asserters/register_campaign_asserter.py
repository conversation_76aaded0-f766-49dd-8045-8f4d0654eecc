import os

from playwright.sync_api import Page

from src.ui.domhandlers.register_campaign_page import RegisterCampaignPage


class RegisterCampaignAsserter(RegisterCampaignPage):
    base_url = os.getenv("BASE_URL")
    success_url = "ui/v2/message?msg=phrase.campaign_submitted&desc=sentence.campaign_submitted" # noqa: E501

    def __init__(self, page: Page):
        super().__init__(page)

    def assert_registration_campaign_is_successful(self):
        self.page.wait_for_url(
            f"{self.base_url}{self.success_url}"
        )
