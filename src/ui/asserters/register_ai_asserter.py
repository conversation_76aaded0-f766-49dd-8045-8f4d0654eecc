import os

from playwright.sync_api import Page

from src.ui.domhandlers.register_ai_page import RegisterAiPage


class RegisterAiAsserter(RegisterAiPage):
    base_url = os.getenv("BASE_URL")
    demo_success_url = "ui/v2/message?msg=phrase.registration_submitted&desc=sentence.registration_ai_demo_submitted" # noqa: E501
    complete_success_url = "ui/v2/message?msg=phrase.registration_submitted&desc=sentence.registration_ai_submitted" # noqa: E501
    def __init__(self, page: Page):
        super().__init__(page)

    def assert_registration_demo_is_successful(self):
        self.page.wait_for_url(f"{self.base_url}{self.demo_success_url}")

    def assert_registration_complete_is_successful(self):
        self.page.wait_for_url(f"{self.base_url}{self.complete_success_url}")
