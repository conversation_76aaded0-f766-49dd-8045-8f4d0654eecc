from playwright.sync_api import Page
from src.ui.domhandlers.register_ai_page import RegisterAiPage

class RegisterAiAsserter(RegisterAiPage):
    def __init__(self, page: Page):
        super().__init__(page)

    def assert_registration_demo_is_successful(self):
        self.page.wait_for_url("https://test.stramigo.com/ui/v2/message?msg=phrase.registration_submitted&desc=sentence.registration_ai_demo_submitted")

    def assert_registration_complete_is_successful(self):
        self.page.wait_for_url("https://test.stramigo.com/ui/v2/message?msg=phrase.registration_submitted&desc=sentence.registration_ai_complete_submitted")