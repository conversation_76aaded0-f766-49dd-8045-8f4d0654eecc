import os

from playwright.sync_api import Page

from src.ui.domhandlers.login_page import LoginPage


class LoginAsserter(LoginPage):
    base_url = os.getenv("BASE_URL")
    def __init__(self, page: Page):
        super().__init__(page)
        self.page = page

    def assert_login_is_successful(self):
        self.page.wait_for_url(f"{self.base_url}dashboard")

    def assert_password_reset_link_is_sent(self):
        self.success_message_reset_password.wait_for()
