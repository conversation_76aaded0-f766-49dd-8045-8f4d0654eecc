# Playwright Test Automation Project

This project contains automated tests using Playwright for both JavaScript/TypeScript and Python.

## Setup

### JavaScript/Node.js Setup

1. Install Node.js dependencies:
```bash
npm install
```

2. Install Playwright browsers:
```bash
npx playwright install
```

### Python Setup

1. Create a virtual environment (recommended):
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install Python dependencies:
```bash
pip install -r requirements.txt
```

3. Install Playwright browsers for Python:
```bash
playwright install
```

## Running Tests

### JavaScript Tests

```bash
# Run all tests
npm test

# Run tests in headed mode (visible browser)
npm run test:headed

# Run tests in debug mode
npm run test:debug

# Run tests with UI mode
npm run test:ui

# Show test report
npm run report
```

### Python Tests

```bash
# Run all Python tests
pytest

# Run tests in headed mode
pytest --headed

# Run specific test file
pytest tests/test_example.py

# Run with verbose output
pytest -v
```

## Project Structure

```
├── tests/                  # Test files
│   ├── example.spec.js    # JavaScript test examples
│   └── test_example.py    # Python test examples
├── playwright.config.js   # Playwright configuration
├── pytest.ini            # Pytest configuration
├── package.json           # Node.js dependencies and scripts
├── requirements.txt       # Python dependencies
├── test.py               # Your original test file
└── README.md             # This file
```

## Configuration

- **playwright.config.js**: Main Playwright configuration for JavaScript tests
- **pytest.ini**: Configuration for Python tests
- Tests run on Chromium, Firefox, and WebKit by default
- Screenshots and videos are captured on test failures
- HTML reports are generated after test runs

## Writing Tests

### JavaScript Example
```javascript
const { test, expect } = require('@playwright/test');

test('example test', async ({ page }) => {
  await page.goto('https://example.com');
  await expect(page).toHaveTitle(/Example/);
});
```

### Python Example
```python
from playwright.sync_api import Page, expect

def test_example(page: Page):
    page.goto("https://example.com")
    expect(page).to_have_title(re.compile("Example"))
```

## Useful Commands

- `npx playwright codegen` - Generate tests by recording browser interactions
- `npx playwright show-trace` - View trace files from failed tests
- `pytest --lf` - Run only the last failed tests
- `pytest -k "test_name"` - Run tests matching a pattern
