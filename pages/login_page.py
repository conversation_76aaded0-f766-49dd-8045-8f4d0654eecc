from pages.base_page import Page, Locator

class Login:
    def __init__(self, page: Page):
        self.page = page
        self.email_input: Locator = page.get_by_test_id("login-email")
        self.password_input: Locator = page.get_by_test_id("login-password")
        self.login_button: Locator = page.get_by_test_id("button-submit-login")
        self.forgot_password_link: Locator = page.get_by_test_id("button-forgot-password")
        self.reset_submit_button: Locator = page.get_by_role("button", name="submit")



    def login(self, username: str, password: str):
        self.email_input.fill(username)
        self.password_input.fill(password)
        self.login_button.click()

    def wait_for_login_success(self):
        self.page.wait_for_load_state("networkidle")
        self.page.wait_for_load_state("domcontentloaded")
