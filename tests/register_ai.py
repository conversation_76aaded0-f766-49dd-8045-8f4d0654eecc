import pytest
from src.ui.steppers.register_ai_stepper import RegisterA<PERSON><PERSON>tepper
from src.ui.asserters.register_ai_asserter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


@pytest.fixture
def ai_stepper(page):
    return RegisterAiStepper(page)


@pytest.fixture
def ai_asserter(page):
    return RegisterAiAsserter(page)


def test_ai_registration_demo(page, ai_stepper, ai_asserter):
    page.goto("https://test.stramigo.com/ui/v2/register?flow=ai_demo")
    ai_stepper.register_ai_demo("Test Organisation", "123456789", "Test Contact", "<EMAIL>")
    ai_asserter.assert_registration_demo_is_successful()


def test_ai_registration_complete(page, ai_stepper, ai_asserter):
    page.goto("https://test.stramigo.com/ui/v2/register?flow=ai_complete")
    ai_stepper.register_ai_complete("Test Organisation", "123456789", "Test Contact", "<EMAIL>")
    ai_asserter.assert_registration_complete_is_successful()