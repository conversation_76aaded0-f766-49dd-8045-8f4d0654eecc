import pytest
import allure
from auth.test_user_provider import Role
from src.ui.asserters.login_asserter import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.ui.steppers.login_stepper import LoginStepper


@pytest.fixture
def login_stepper(page):
    return LoginStepper(page)

@pytest.fixture
def login_asserter(page):
    return LoginAsserter(page)


@pytest.mark.ui
@allure.title("UI - Login with admin credentials")
@allure.description("Login with admin credentials")
def test_login_success(page, base_url, login_stepper, login_asserter, user_provider):
    with allure.step("Navigate to login page"):
        page.goto(f"{base_url}ui/v2/login")
    with allure.step("Login with admin credentials"):
        login_stepper.login_with_role(Role.ADMIN, user_provider)
    with allure.step("Assert login is successful"):
        login_asserter.assert_login_is_successful()


@pytest.mark.ui
@allure.title("UI - Request password reset")
@allure.description("Request password reset")
def test_forgot_password(page, base_url, login_stepper, login_asserter, user_provider):
    with allure.step("Navigate to login page"):
        page.goto(f"{base_url}ui/v2/login")
    with allure.step("Request password reset"):
        login_stepper.request_password_reset(Role.ADMIN, user_provider)
    with allure.step("Assert password link is sent"):
        login_asserter.assert_password_reset_link_is_sent()
