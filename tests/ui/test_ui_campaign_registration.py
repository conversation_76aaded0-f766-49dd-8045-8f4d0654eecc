import pytest
import allure

from src.ui.asserters.register_campaign_asserter import RegisterCampaignAsserter
from src.ui.steppers.register_campaign_stepper import Register<PERSON>ampaign<PERSON>tepper


@pytest.fixture
def campaign_stepper(page):
    return RegisterCampaignStepper(page)


@pytest.fixture
def campaign_asserter(page):
    return RegisterCampaignAsserter(page)


@pytest.mark.ui
@pytest.mark.dependency(depends=["api_campaign_registration"], scope="session")
@allure.title("UI - Register campaign")
@allure.description("Register campaign")
def test_campaign_registration(
        page,
        base_url,
        campaign_stepper,
        campaign_asserter,
        data_generator
):
    with allure.step("Navigate to campaign registration page"):
        page.goto(f"{base_url}ui/v2/register?flow=campaign")
    with allure.step("Fill out campaign registration form"):
        campaign_stepper.register_campaign(
            data_generator.campaign_name,
            data_generator.description,
            data_generator.future_date,
            data_generator.company,
            data_generator.name,
            data_generator.email,
        )
    with allure.step("Assert registration is successful"):
        campaign_asserter.assert_registration_campaign_is_successful()

