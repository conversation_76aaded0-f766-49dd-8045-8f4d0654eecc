import pytest
import allure

from src.api.helpers.logged_api_request_context import LoggedAPIRequestContext
from src.api.helpers.test_data_generator import DataGenerator
from src.api.models.registration import (
    AiRegistrationRequest,
    AiRegistrationResponse,
    CampaignRegistrationRequest,
    CampaignRegistrationResponse,
)
from src.api.utils.api_response_handler import ApiResponseHandler


@pytest.mark.api
@pytest.mark.dependency(name="api_demo_ai_registration", scope="session")
@allure.title("API - Register AI demo")
@allure.description("Register AI demo via API")
def test_demo_ai_registration(
    api_request_context: LoggedAPIRequestContext,
        data_generator: DataGenerator,
        base_url
):
    request = AiRegistrationRequest(
        accept_conditions=True,
        accept_price=False,
        accept_privacy_statement=True,
        contact_email=data_generator.email,
        contact_name=data_generator.name,
        flow="ai_demo",
        organisation_coc=data_generator.coc,
        organisation_name=data_generator.company,
    )
    with allure.step("Send AI demo registration request"):
        response = api_request_context.post(
            f"{base_url}api/org/registrations/ai/", data=request.model_dump()
        )

    with allure.step("Assert AI demo registration is successful"):
        ApiResponseHandler.handle_success(response, 200, AiRegistrationResponse)


@pytest.mark.api
@pytest.mark.dependency(name="api_complete_ai_registration")
@allure.title("API - Register AI complete")
@allure.description("Register AI complete via API")
def test_complete_ai_registration(
    api_request_context: LoggedAPIRequestContext,
        data_generator: DataGenerator,
        base_url
):
    request = AiRegistrationRequest(
        accept_conditions=True,
        accept_price=True,
        accept_privacy_statement=True,
        contact_email=data_generator.email,
        contact_name=data_generator.name,
        flow="ai_complete",
        organisation_coc=data_generator.coc,
        organisation_name=data_generator.company,
    )
    with allure.step("Send AI complete registration request"):
        response = api_request_context.post(
            f"{base_url}api/org/registrations/ai/", data=request.model_dump()
        )

    with allure.step("Assert AI complete registration is successful"):
        ApiResponseHandler.handle_success(response, 200, AiRegistrationResponse)


@pytest.mark.api
@pytest.mark.dependency(name="api_campaign_registration")
@allure.title("API - Register campaign")
@allure.description("Register campaign via API")
def test_campaign_registration(
    api_request_context: LoggedAPIRequestContext,
        data_generator: DataGenerator,
        base_url
):
    request = CampaignRegistrationRequest(
        name_campaign=data_generator.campaign_name,
        date_campaign=data_generator.future_date,
        description_campaign=data_generator.description,
        organisation_name=data_generator.company,
        contact_name=data_generator.name,
        contact_email=data_generator.email,
        accept_contact=True,
        accept_price=True,
    )
    with allure.step("Send campaign registration request"):
        response = api_request_context.post(
            f"{base_url}api/org/registrations/campaign/",
            data=request.model_dump(),
        )

    with allure.step("Assert campaign registration is successful"):
        ApiResponseHandler.handle_success(response, 200, CampaignRegistrationResponse)
