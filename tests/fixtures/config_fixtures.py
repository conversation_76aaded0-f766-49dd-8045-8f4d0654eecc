import os
import allure
from typing import Generator

import pytest
from dotenv import load_dotenv
from playwright.sync_api import <PERSON><PERSON>

from src.api.helpers.logged_api_request_context import Logged<PERSON>IRequestContext
from src.api.helpers.test_data_generator import DataGenerator

load_dotenv(override=True)

@pytest.fixture(scope="session", autouse=True)
def set_test_id_attribute(playwright: Playwright):
    playwright.selectors.set_test_id_attribute("id")

@pytest.fixture(scope="session")
def api_request_context(
    playwright: Playwright,
) -> Generator[LoggedAPIRequestContext, None, None]:
    request_context = LoggedAPIRequestContext(playwright.request.new_context())
    yield request_context
    request_context.dispose()

@pytest.fixture(scope="session")
def data_generator() -> DataGenerator:
    return DataGenerator()

@pytest.fixture(scope="session")
def base_url():
    return os.getenv("BASE_URL")

@pytest.fixture(scope="session")
def admin_email():
    return os.getenv("EMAIL_ADMIN")

@pytest.fixture(scope="session")
def admin_password():
    return os.getenv("PASSWORD_ADMIN")

@pytest.fixture(scope="session")
def user_email():
    return os.getenv("EMAIL_USER")

@pytest.fixture(scope="session")
def user_password():
    return os.getenv("PASSWORD_USER")

def check_ui_test_for_stepper_and_asserter(items):
    for item in items:
        fixtures = set(item.fixturenames)
        markers = item.own_markers
        if "name='api'" in str(markers):
            continue

        has_stepper = any(f.endswith("_stepper") for f in fixtures)
        has_asserter = any(f.endswith("_asserter") for f in fixtures)

        if not has_stepper or not has_asserter:
            missing = []
            if not has_stepper:
                missing.append("stepper")
            if not has_asserter:
                missing.append("asserter")

            raise pytest.UsageError(f"Test {item.name} is missing {missing}")


@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_make_report(item):
    outcome = yield
    rep = outcome.get_result()

    if rep.when == "call" and rep.failed:
        page = item.funcargs.get("page", None)
        if page:
            with allure.step("Attach screenshot on failure"):
                allure.attach(
                    page.screenshot(),
                    name="screenshot_on_failure",
                    attachment_type=allure.attachment_type.PNG
                )