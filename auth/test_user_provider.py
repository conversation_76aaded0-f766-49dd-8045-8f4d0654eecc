import enum
from dataclasses import dataclass


class Role(enum.Enum):
    ADMIN = "admin"
    USER = "user"


@dataclass
class TestUser:
    email: str | None = None
    password: str | None = None

class UserProvider:
    def __init__(
            self,
            admin_email: str,
            admin_password: str,
            user_email: str,
            user_password: str
    ):
        self.admin_email = admin_email
        self.admin_password = admin_password
        self.user_email = user_email
        self.user_password = user_password

    def get_test_user(self, role: Role) -> TestUser:
        if role == Role.ADMIN:
            return TestUser(
                email=self.admin_email,
                password=self.admin_password,
            )
        elif role == Role.USER:
            return TestUser(
                email=self.user_email,
                password=self.user_password,
            )
        else:
            raise ValueError(f"Role {role} is not supported")
