import os
import enum
from dataclasses import dataclass
from typing import Optional, List

from configuration.env_vars import EnvVars


class Role(enum.Enum):
    ADMIN = "admin"
    USER = "user"
    ANONYMOUS = "anonymous"


@dataclass
class TestUser:
    email: Optional[str] = None
    password: Optional[str] = None
    auth_file_ui: Optional[str] = None
    role: Optional[str] = None


class TestUserProvider:
    @staticmethod
    def get_active_test_users() -> List[TestUser]:
        return [
            TestUserProvider.get_test_user(Role.ADMIN),
            TestUserProvider.get_test_user(Role.USER)
        ]

    @staticmethod
    def get_test_user(role: Role) -> TestUser:
        if role == Role.ADMIN:
            return TestUser(
                email=EnvVars.EMAIL_ADMIN,
                password=EnvVars.PASSWORD_ADMIN,
                auth_file_ui=TestUserProvider.get_auth_file_ui(role),
                role=role.value
            )
        elif role == Role.USER:
            return TestUser(
                email=EnvVars.EMAIL_USER,
                password=EnvVars.PASSWORD_USER,
                auth_file_ui=TestUserProvider.get_auth_file_ui(role),
                role=role.value
            )
        elif role == Role.ANONYMOUS:
            return TestUser(
                role=role.value
            )
        else:
            raise ValueError(f"Role {role} is not supported")

    @staticmethod
    def get_auth_file_ui(role: Role) -> str:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        return os.path.join(current_dir, f"./ui/user-{role.value}.json")