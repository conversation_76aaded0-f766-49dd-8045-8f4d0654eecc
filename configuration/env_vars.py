import os
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseSettings, AnyUrl, EmailStr, ValidationError, validator


class EnvVars(BaseSettings):
    LOG_ENVIRONMENT: bool = False
    LOG_API: bool = False
    BASE_URL_UI: AnyUrl
    BASE_URL_API: AnyUrl
    EMAIL_ADMIN: EmailStr
    PASSWORD_ADMIN: str
    EMAIL_USER: EmailStr
    PASSWORD_USER: str

    @validator("LOG_ENVIRONMENT", "LOG_API", pre=True)
    def parse_bool(cls, v):
        if isinstance(v, bool):
            return v
        if v is None:
            return False
        v = str(v).strip().lower()
        if v in {"true", "1", "yes"}:
            return True
        if v in {"false", "0", "no"}:
            return False
        raise ValueError("Must be 'true' or 'false'")


# 1. Laad basis .env
load_dotenv(".env")

# 2. Check of er een override is
override_env = os.getenv("ENVIRONMENT", "").lower()
if override_env:
    if override_env not in {"dev", "tst", "acc", "loc"}:
        raise ValueError(f"Invalid ENVIRONMENT: {override_env}")
    # extra bestand inladen en bestaande waarden overschrijven
    load_dotenv(f".env.{override_env}", override=True)

try:
    env_vars = EnvVars()
except ValidationError as e:
    print("Invalid environment variables:")
    print(e)
    raise SystemExit(1)

if env_vars.LOG_ENVIRONMENT:
    print(f"Environment loaded:\n{override_env.upper() if override_env else 'DEFAULT'}")